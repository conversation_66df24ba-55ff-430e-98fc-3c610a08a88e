"use client";

import { useEffect, useMemo, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/components/auth-provider";
import { Search, ChevronLeft, ChevronRight } from "lucide-react";
import Link from "next/link";
import { format } from "date-fns";
import AppShell from "@/components/app-shell";

type MailListItem = {
  uid: number;
  subject: string;
  from: string;
  recipients: string[];
  date: string;
  seen: boolean;
};

type ListResponse = {
  items: MailListItem[];
  total: number;
  page: number;
  pageSize: number;
};

export default function InboxPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pageParam = Number(searchParams.get("page") || "1");
  const qParam = searchParams.get("q") || "";
  const pageSizeParam = Number(searchParams.get("pageSize") || "20");

  const { initializing, login, getAuthHeader } = useAuth();
  const [data, setData] = useState<ListResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [q, setQ] = useState(qParam);
  const [pageSize, setPageSize] = useState(pageSizeParam);

  const totalPages = useMemo(() => {
    if (!data) return 1;
    return Math.max(1, Math.ceil(data.total / data.pageSize));
  }, [data]);

  useEffect(() => {
    if (initializing) return;
    const load = async () => {
      setLoading(true);
      try {
        const auth = await getAuthHeader();
        const url = new URL("/api/mail/list", window.location.origin);
        url.searchParams.set("page", String(pageParam));
        url.searchParams.set("pageSize", String(pageSizeParam));
        if (qParam) url.searchParams.set("q", qParam);
        const res = await fetch(url.toString(), {
          headers: {
            ...(auth ? { Authorization: auth } : {}),
          },
        });
        if (res.status === 401) {
          // trigger login flow; return to avoid setting state after redirect
          return login();
        }
        if (res.status === 403) {
          // forbidden: missing role
          return router.replace("/auth/no-role");
        }
        if (!res.ok) throw new Error(await res.text());
        const json = (await res.json()) as ListResponse;
        setData(json);
      } catch (e) {
        console.error(e);
        setData({
          items: [],
          total: 0,
          page: pageParam,
          pageSize: pageSizeParam,
        });
      } finally {
        setLoading(false);
      }
    };
    load();
  }, [
    initializing,
    getAuthHeader,
    pageParam,
    qParam,
    pageSizeParam,
    login,
    router,
  ]);

  // Optionally show a tiny initializing state
  if (initializing) {
    return (
      <div className="p-6 max-w-xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>Loading...</CardTitle>
          </CardHeader>
          <CardContent>Initializing authentication...</CardContent>
        </Card>
      </div>
    );
  }

  return (
    <AppShell pageTitle="Inbox">
      <div className="mx-auto max-w-5xl">
        <div className="flex flex-col md:flex-row gap-2 md:items-center md:justify-between mb-4">
          <form
            className="flex-1 flex items-center gap-2"
            onSubmit={(e) => {
              e.preventDefault();
              const sp = new URLSearchParams(window.location.search);
              if (q) sp.set("q", q);
              else sp.delete("q");
              sp.set("page", "1");
              sp.set("pageSize", String(pageSize));
              router.push(`/inbox?${sp.toString()}`);
            }}
          >
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={
                  "Filter by recipient (To/Cc/Bcc/Delivered-To/X-Forwarded-To)"
                }
                className="pl-8"
                value={q}
                onChange={(e) => setQ(e.target.value)}
              />
            </div>
            <Input
              type="number"
              min={5}
              max={100}
              value={pageSize}
              onChange={(e) => setPageSize(Number(e.target.value))}
              className="w-24"
            />
            <Button type="submit">{"Apply"}</Button>
          </form>
          <div className="text-sm text-muted-foreground">
            {data ? `${data.total} messages` : "—"}
          </div>
        </div>

        <Card>
          <CardContent className="p-0">
            <ul className="divide-y">
              {loading && (
                <li className="p-4 text-sm text-muted-foreground">
                  {"Loading..."}
                </li>
              )}
              {!loading && data?.items.length === 0 && (
                <li className="p-4 text-sm text-muted-foreground">
                  {"No messages found."}
                </li>
              )}
              {!loading &&
                data?.items.map((item) => (
                  <li
                    key={item.uid}
                    className="p-3 hover:bg-muted/50 transition"
                  >
                    <Link
                      href={`/inbox/${item.uid}`}
                      className="block"
                    >
                      <div className="flex items-center justify-between gap-3">
                        <div className="min-w-0">
                          <div className="flex items-center gap-2">
                            {!item.seen && (
                              <Badge variant="default">{"NEW"}</Badge>
                            )}
                            <span className="font-medium truncate">
                              {item.subject || "(no subject)"}
                            </span>
                          </div>
                          <div className="text-xs sm:text-sm text-muted-foreground truncate">
                            {"From: "}
                            {item.from}
                          </div>
                          <div className="text-[11px] sm:text-xs text-muted-foreground truncate">
                            {"To: "}
                            {item.recipients.join(", ")}
                          </div>
                        </div>
                        <div className="shrink-0 text-[11px] sm:text-xs text-muted-foreground text-right">
                          {format(new Date(item.date), "yyyy-MM-dd HH:mm")}
                        </div>
                      </div>
                    </Link>
                  </li>
                ))}
            </ul>
          </CardContent>
        </Card>

        <div className="flex items-center justify-between mt-4">
          <div className="text-sm text-muted-foreground">
            {data ? `Page ${data.page} of ${totalPages}` : "—"}
          </div>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                const sp = new URLSearchParams(window.location.search);
                sp.set("page", String(Math.max(1, pageParam - 1)));
                router.push(`/inbox?${sp.toString()}`);
              }}
              disabled={pageParam <= 1}
            >
              <ChevronLeft className="h-4 w-4 mr-1" />
              {"Prev"}
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                const sp = new URLSearchParams(window.location.search);
                sp.set("page", String(pageParam + 1));
                router.push(`/inbox?${sp.toString()}`);
              }}
              disabled={data ? pageParam >= totalPages : true}
            >
              {"Next"}
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
        </div>
      </div>
    </AppShell>
  );
}
