import type { NextRequest } from "next/server"
import { getServerEnv } from "@/lib/config"
import { ensureRole, verifyAuth } from "@/lib/auth"
import { listMessages, type MailListItem } from "@/lib/imap"
import { withImapClient } from "@/lib/imap"

export async function GET(req: NextRequest) {
  try {
    const auth = await verifyAuth(req)
    const { requiredRole, imap } = getServerEnv()
    ensureRole(auth, requiredRole)

    const { searchParams } = new URL(req.url)
    const page = Number(searchParams.get("page") || "1")
    const pageSize = Number(searchParams.get("pageSize") || "20")
    const q = (searchParams.get("q") || "").trim()

    // If IMAP credentials are not configured, return empty data
    if (!imap.user || !imap.pass) {
      return Response.json({ items: [], total: 0, page, pageSize }, { status: 200 })
    }

    // Add timeout wrapper for the entire operation
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error("Request timeout")), 120000) // 2 minutes timeout
    })

    const resultPromise = withImapClient(imap, async (client) => {
      return await listMessages(client, { page, pageSize, recipientFilter: q || undefined })
    })

    const result = await Promise.race([resultPromise, timeoutPromise])

    return Response.json(result, { status: 200 })
  } catch (err: any) {
    const status = Number(err?.status) || 500
    let message = typeof err?.message === "string" ? err.message : "Internal Server Error"

    // Handle timeout errors specifically
    if (err?.message === "Request timeout" || err?.code === "ETIMEOUT") {
      message = "Request timeout - try reducing the search scope or check your connection"
      return new Response(message, { status: 408 })
    }

    return new Response(message, { status })
  }
}


