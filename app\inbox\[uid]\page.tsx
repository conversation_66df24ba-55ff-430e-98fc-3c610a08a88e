"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/components/auth-provider";
import { ArrowLeft, Download } from "lucide-react";
import sanitizeHtml from "sanitize-html";
import AppShell from "@/components/app-shell";

type Attachment = {
  partId: string;
  filename: string;
  contentType: string;
  size: number;
};

type MailDetail = {
  uid: number;
  subject: string;
  from: string;
  to: string[];
  cc: string[];
  bcc: string[];
  deliveredTo: string[];
  xForwardedTo: string[];
  date: string;
  html?: string;
  text?: string;
  attachments: Attachment[];
};

export default function MailDetailPage() {
  const router = useRouter();
  const { uid } = useParams<{ uid: string }>();
  const { getAuthHeader, initializing, login } = useAuth();
  const [data, setData] = useState<MailDetail | null>(null);
  const [loading, setLoading] = useState(false);

  const handleDownload = async (att: Attachment) => {
    try {
      const auth = await getAuthHeader();
      const url = new URL("/api/mail/attachment", window.location.origin);
      url.searchParams.set("uid", String(data?.uid ?? uid));
      url.searchParams.set("partId", att.partId);
      const res = await fetch(url.toString(), {
        headers: {
          ...(auth ? { Authorization: auth } : {}),
        },
      });
      if (res.status === 401) return login();
      if (res.status === 403) return router.replace("/auth/no-role");
      if (!res.ok) throw new Error(await res.text());
      const blob = await res.blob();
      const href = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = href;
      a.download =
        att.filename || `attachment-${String(data?.uid ?? uid)}-${att.partId}`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      URL.revokeObjectURL(href);
    } catch (e) {
      console.error(e);
    }
  };

  useEffect(() => {
    if (initializing || !uid) return;
    const load = async () => {
      setLoading(true);
      try {
        const auth = await getAuthHeader();
        const url = new URL("/api/mail/detail", window.location.origin);
        url.searchParams.set("uid", uid);
        const res = await fetch(url.toString(), {
          headers: {
            ...(auth ? { Authorization: auth } : {}),
          },
        });
        if (res.status === 401) return login();
        if (res.status === 403) return router.replace("/auth/no-role");
        if (!res.ok) throw new Error(await res.text());
        const json = (await res.json()) as MailDetail;
        setData(json);
      } catch (e) {
        console.error(e);
      } finally {
        setLoading(false);
      }
    };
    load();
  }, [uid, initializing, getAuthHeader, login, router]);

  return (
    <AppShell pageTitle="Mail Detail">
      <div className="max-w-4xl mx-auto">
        <div className="mb-3">
          <Button
            variant="ghost"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        </div>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {data?.subject || "(no subject)"}
              {/* Could indicate read state with a badge if needed */}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {loading && (
              <div className="text-sm text-muted-foreground">Loading...</div>
            )}
            {data && (
              <>
                <div className="text-sm">
                  <div>
                    <span className="font-medium">From: </span>
                    <span>{data.from}</span>
                  </div>
                  <div>
                    <span className="font-medium">To: </span>
                    <span>{data.to.join(", ") || "—"}</span>
                  </div>
                  {data.cc.length > 0 && (
                    <div>
                      <span className="font-medium">Cc: </span>
                      <span>{data.cc.join(", ")}</span>
                    </div>
                  )}
                  {data.bcc.length > 0 && (
                    <div>
                      <span className="font-medium">Bcc: </span>
                      <span>{data.bcc.join(", ")}</span>
                    </div>
                  )}
                  {(data.deliveredTo.length > 0 ||
                    data.xForwardedTo.length > 0) && (
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">Routed</Badge>
                      <span className="text-xs text-muted-foreground">
                        Delivered-To: {data.deliveredTo.join(", ")}{" "}
                        X-Forwarded-To: {data.xForwardedTo.join(", ")}
                      </span>
                    </div>
                  )}
                </div>
                {data.html ? (
                  <div
                    className="prose max-w-none"
                    dangerouslySetInnerHTML={{
                      __html: sanitizeHtml(data.html, {
                        allowedSchemesByTag: {},
                      }),
                    }}
                  />
                ) : (
                  <pre className="text-sm whitespace-pre-wrap">
                    {data.text || ""}
                  </pre>
                )}
                <div>
                  <div className="font-medium mb-2">Attachments</div>
                  {data.attachments.length === 0 && (
                    <div className="text-sm text-muted-foreground">
                      No attachments
                    </div>
                  )}
                  <ul className="space-y-2">
                    {data.attachments.map((att) => (
                      <li
                        key={att.partId}
                        className="flex items-center justify-between gap-3"
                      >
                        <div className="min-w-0">
                          <div className="truncate">
                            {att.filename || "(unnamed)"}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {att.contentType} • {Math.round(att.size / 1024)} KB
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDownload(att)}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                      </li>
                    ))}
                  </ul>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </AppShell>
  );
}
